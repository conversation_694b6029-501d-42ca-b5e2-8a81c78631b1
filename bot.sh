#!/bin/bash
# Simplified Trading Bot Manager - CLI Interface Only
# All logic moved to Python modules

set -e

# Script Version
SCRIPT_VERSION="3.0.0"
VERSION_CHECK_ENABLED=${BOT_VERSION_CHECK:-true}

# Configuration
REGISTRY=${DOCKER_REGISTRY:-"ghcr.io/hoangtrung99"}
IMAGE_BASE=${IMAGE_BASE:-"autotrader"}
VERSION=${VERSION:-"latest"}

# Define separate images for telegram and trader
TELEGRAM_IMAGE="$REGISTRY/$IMAGE_BASE-telegram:$VERSION"
TRADER_IMAGE="$REGISTRY/$IMAGE_BASE-trader:$VERSION"

# Backward compatibility
FULL_IMAGE="$TRADER_IMAGE"  # Default to trader image for existing functions

# Telegram Configuration
TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-""}
TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID:-""}
TELEGRAM_ENABLED=${TELEGRAM_ENABLED:-true}

# Check if Telegram is available
check_telegram_available() {
    if [[ "$TELEGRAM_ENABLED" != "true" ]]; then
        return 1
    fi
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        return 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        return 1
    fi
    
    return 0
}

# Send Telegram notification
send_telegram_notification() {
    local message="$1"
    local command="${2:-""}"
    local returncode="${3:-0}"
    local stdout="${4:-""}"
    local stderr="${5:-""}"
    
    if ! check_telegram_available; then
        return 0
    fi
    
    # Activate virtual environment if available
    if [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi
    
    if [[ -n "$command" ]]; then
        # Send command response
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --command "$command" \
            --returncode "$returncode" \
            --stdout "$stdout" \
            --stderr "$stderr" &
    else
        # Send simple message
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --message "$message" &
    fi
}

# Version check and enforcement (simplified)
check_version_and_enforce_upgrade() {
    if [[ "$VERSION_CHECK_ENABLED" != "true" ]] || [[ "$1" == "upgrade" ]] || [[ "$1" == "help" ]] || [[ "$1" == "" ]]; then
        return 0
    fi
    
    echo "✅ Version check passed (v$SCRIPT_VERSION)"
}

# Execute Python command with proper error handling and response formatting
execute_python_command() {
    local command=("$@")

    # Activate virtual environment if available
    if [[ -f "/opt/venv/bin/activate" ]]; then
        source /opt/venv/bin/activate
    elif [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi

    # Execute the Python command
    local temp_stdout=$(mktemp)
    local temp_stderr=$(mktemp)

    "${command[@]}" >"$temp_stdout" 2>"$temp_stderr"
    local returncode=$?

    local stdout=$(cat "$temp_stdout")
    local stderr=$(cat "$temp_stderr")

    # Cleanup temp files
    rm -f "$temp_stdout" "$temp_stderr"

    if [[ $returncode -eq 0 ]]; then
        if [[ -n "$stdout" ]]; then
            echo "$stdout"
        fi
    else
        local error_msg="$stderr"
        [[ -z "$error_msg" ]] && error_msg="$stdout"
        [[ -z "$error_msg" ]] && error_msg="Command failed with exit code $returncode"
        echo "❌ Error: $error_msg"
    fi

    return $returncode
}

# Check Docker availability
check_docker() {
    # If running inside Docker container, assume Docker is available via socket
    if [ -f /.dockerenv ]; then
        echo "🐳 Running inside Docker container"
        # Always use sudo in container since docker socket permissions are complex
        export DOCKER_CMD="sudo docker"

        # Test the docker command
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    # Check if Docker socket is available
    if [ -S /var/run/docker.sock ]; then
        export DOCKER_CMD="docker"
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    if ! command -v docker >/dev/null 2>&1; then
        echo "❌ Docker not found. Please install Docker."
        return 1
    fi

    export DOCKER_CMD="docker"
    if ! $DOCKER_CMD version >/dev/null 2>&1; then
        echo "❌ Cannot connect to Docker daemon."
        return 1
    fi

    return 0
}

# Generate container name from symbol
generate_container_name() {
    local symbol="$1"
    if [[ -z "$symbol" ]]; then
        echo "crypto-trading-bot"
    else
        local base_symbol=$(echo "$symbol" | cut -d'/' -f1 | cut -d':' -f1)
        local normalized=$(echo "$base_symbol" | tr '[:upper:]' '[:lower:]')
        echo "$normalized"
    fi
}

# Parse trading arguments
parse_trading_arguments() {
    TRADE_SYMBOL=""
    TRADE_AMOUNT=""
    TRADE_EXCHANGE=""
    TRADE_DIRECTION=""
    TRADE_TEST_MODE=""
    TRADE_STOP_LOSS=""
    TRADE_TAKE_PROFIT=""
    TRADE_API_KEY=""
    TRADE_API_SECRET=""
    
    # Check if first argument is a symbol
    if [[ $# -gt 0 ]] && [[ ! "$1" =~ ^-- ]]; then
        TRADE_SYMBOL="$1"
        shift
    fi
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --symbol)
                TRADE_SYMBOL="$2"
                shift 2
                ;;
            --amount)
                TRADE_AMOUNT="$2"
                shift 2
                ;;
            --exchange)
                TRADE_EXCHANGE="$2"
                shift 2
                ;;
            --direction)
                TRADE_DIRECTION="$2"
                shift 2
                ;;
            --test-mode)
                TRADE_TEST_MODE="true"
                shift
                ;;
            --stop-loss)
                TRADE_STOP_LOSS="$2"
                shift 2
                ;;
            --take-profit)
                TRADE_TAKE_PROFIT="$2"
                shift 2
                ;;
            --key)
                TRADE_API_KEY="$2"
                export BYBIT_API_KEY="$2"
                shift 2
                ;;
            --secret)
                TRADE_API_SECRET="$2"
                export BYBIT_API_SECRET="$2"
                shift 2
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # Normalize symbol format
    if [[ -n "$TRADE_SYMBOL" ]]; then
        if [[ "$TRADE_SYMBOL" != *"/"* ]]; then
            local normalized_symbol=$(echo "${TRADE_SYMBOL}" | tr '[:lower:]' '[:upper:]')
            TRADE_SYMBOL="${normalized_symbol}/USDT:USDT"
            echo "📝 Normalized symbol: $TRADE_SYMBOL"
        fi
    fi
}

# Update Docker images
update_docker_images() {
    local mode="${1:-both}"  # telegram, trader, or both

    case "$mode" in
        telegram)
            echo "🔄 Updating Telegram bot image: $TELEGRAM_IMAGE"
            if $DOCKER_CMD pull "$TELEGRAM_IMAGE"; then
                echo "✅ Telegram image updated successfully"
                send_telegram_notification "Telegram bot image updated: $TELEGRAM_IMAGE"
            else
                echo "⚠️ Failed to update Telegram image, using existing version"
            fi
            ;;
        trader)
            echo "🔄 Updating Trader bot image: $TRADER_IMAGE"
            if $DOCKER_CMD pull "$TRADER_IMAGE"; then
                echo "✅ Trader image updated successfully"
                send_telegram_notification "Trader bot image updated: $TRADER_IMAGE"
            else
                echo "⚠️ Failed to update Trader image, using existing version"
            fi
            ;;
        both|*)
            echo "🔄 Updating both Docker images..."
            update_docker_images telegram
            update_docker_images trader
            ;;
    esac
}

# Run Docker container
run_docker_container() {
    local detach_mode="${1:-true}"
    local container_name="${2:-crypto-trading-bot}"
    
    # Remove existing container if it exists
    $DOCKER_CMD stop "$container_name" 2>/dev/null || true
    $DOCKER_CMD rm "$container_name" 2>/dev/null || true

    # Build docker run command
    local docker_run_cmd="$DOCKER_CMD run"
    
    if [[ "$detach_mode" == "true" ]]; then
        docker_run_cmd+=" -d"
    fi

    docker_run_cmd+=" --name $container_name"
    docker_run_cmd+=" --restart unless-stopped"
    
    # Add environment variables
    [[ -n "$TRADE_SYMBOL" ]] && docker_run_cmd+=" -e TRADE_SYMBOL='$TRADE_SYMBOL'"
    [[ -n "$TRADE_AMOUNT" ]] && docker_run_cmd+=" -e TRADE_AMOUNT='$TRADE_AMOUNT'"
    [[ -n "$TRADE_EXCHANGE" ]] && docker_run_cmd+=" -e TRADE_EXCHANGE='$TRADE_EXCHANGE'"
    [[ -n "$TRADE_DIRECTION" ]] && docker_run_cmd+=" -e TRADE_DIRECTION='$TRADE_DIRECTION'"
    [[ -n "$TRADE_TEST_MODE" ]] && docker_run_cmd+=" -e TRADE_TEST_MODE='$TRADE_TEST_MODE'"
    [[ -n "$TRADE_STOP_LOSS" ]] && docker_run_cmd+=" -e TRADE_STOP_LOSS='$TRADE_STOP_LOSS'"
    [[ -n "$TRADE_TAKE_PROFIT" ]] && docker_run_cmd+=" -e TRADE_TAKE_PROFIT='$TRADE_TAKE_PROFIT'"
    [[ -n "$BYBIT_API_KEY" ]] && docker_run_cmd+=" -e BYBIT_API_KEY='$BYBIT_API_KEY'"
    [[ -n "$BYBIT_API_SECRET" ]] && docker_run_cmd+=" -e BYBIT_API_SECRET='$BYBIT_API_SECRET'"

    docker_run_cmd+=" $TRADER_IMAGE"
    
    echo "🚀 Starting container: $container_name"
    echo "🖼️  Image: $TRADER_IMAGE"
    echo "Command: $docker_run_cmd"

    if eval "$docker_run_cmd"; then
        echo "✅ Container started successfully: $container_name"
        send_telegram_notification "Trading bot started: $container_name" "start" 0 "Container: $container_name\\nSymbol: $TRADE_SYMBOL\\nAmount: $TRADE_AMOUNT"
        return 0
    else
        echo "❌ Failed to start container: $container_name"
        send_telegram_notification "Failed to start trading bot" "start" 1 "" "Container start failed: $container_name"
        return 1
    fi
}

# Start bot
start_bot() {
    echo "🤖 Starting Trading Bot"
    echo "======================="
    
    parse_trading_arguments "$@"
    
    if [[ -z "$TRADE_SYMBOL" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 start <symbol> [options]"
        return 1
    fi
    
    if ! check_docker; then
        return 1
    fi
    
    CONTAINER_NAME=$(generate_container_name "$TRADE_SYMBOL")
    
    echo "📊 Trading Configuration:"
    echo "   Symbol: $TRADE_SYMBOL"
    echo "   Amount: ${TRADE_AMOUNT:-default}"
    echo "   Container: $CONTAINER_NAME"
    echo "   Test Mode: ${TRADE_TEST_MODE:-false}"
    
    update_docker_images trader
    run_docker_container true "$CONTAINER_NAME"
}

# Stop bot
stop_bot() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 stop <symbol>"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    echo "⏹️ Stopping trading bot: $container_name"

    if $DOCKER_CMD stop "$container_name" 2>/dev/null; then
        echo "✅ Container stopped: $container_name"
        send_telegram_notification "Trading bot stopped: $container_name"
    else
        echo "⚠️ Container not running: $container_name"
    fi

    if $DOCKER_CMD rm "$container_name" 2>/dev/null; then
        echo "✅ Container removed: $container_name"
    fi
}

# Restart bot
restart_bot() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 restart <symbol>"
        return 1
    fi
    
    echo "🔄 Restarting bot for symbol: $symbol"
    
    stop_bot "$symbol"
    sleep 2
    
    # Load previous config if available
    local container_name=$(generate_container_name "$symbol")
    local config_file="$HOME/.traderbot/configs/${container_name}.env"
    
    if [[ -f "$config_file" ]]; then
        echo "📋 Loading previous configuration..."
        source "$config_file"
    fi
    
    start_bot "$symbol" --amount "${TRADE_AMOUNT:-50}"
}

# List containers
list_containers() {
    echo "📊 Trading Bot Containers"
    echo "========================="
    
    if ! check_docker; then
        return 1
    fi
    
    local containers=$($DOCKER_CMD ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" | grep -E "(autotrader|crypto|trading)" || true)
    
    if [[ -z "$containers" ]]; then
        echo "📭 No trading bot containers found"
        echo ""
        echo "💡 Start a bot with:"
        echo "   $0 start <symbol> --amount <amount>"
        return 0
    fi
    
    echo "$containers"
}

# Get bot status
get_status() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        list_containers
        return 0
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    if ! $DOCKER_CMD ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo "❌ No container found for symbol: $symbol"
        return 1
    fi

    echo "📊 Status for $symbol:"
    echo "===================="
    $DOCKER_CMD ps -a --filter name="$container_name" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"
}

# Get bot logs
get_logs() {
    local symbol="$1"
    local lines="${2:-50}"
    
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 logs <symbol> [lines]"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    if ! $DOCKER_CMD ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo "❌ No container found for symbol: $symbol"
        return 1
    fi

    echo "📋 Last $lines lines for $symbol:"
    echo "==============================="
    $DOCKER_CMD logs --tail "$lines" "$container_name"
}

# Start Telegram bot
start_telegram_bot() {
    echo "📱 Starting Telegram Bot"
    echo "========================"
    
    if ! check_telegram_available; then
        echo "❌ Telegram not configured properly"
        echo "Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID"
        return 1
    fi
    
    echo "🚀 Starting Telegram bot..."
    echo "   Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
    echo "   Chat ID: $TELEGRAM_CHAT_ID"
    
    execute_python_command python3 run_telegram_app.py start \
        --token "$TELEGRAM_BOT_TOKEN" \
        --chat-id "$TELEGRAM_CHAT_ID"
}

# Deploy to server mode
deploy_server() {
    echo "🚀 Deploying AutoTrader to Server Mode"
    echo "======================================"
    
    local mode="${1:-both}"  # telegram, trader, or both
    
    case "$mode" in
        telegram)
            deploy_telegram_bot
            ;;
        trader)
            deploy_trader_bot
            ;;
        both|*)
            deploy_telegram_bot
            deploy_trader_bot
            ;;
    esac
}

# Deploy telegram bot
deploy_telegram_bot() {
    echo "📱 Deploying Telegram Bot"
    echo "========================="
    
    if ! check_docker; then
        return 1
    fi
    
    # Pull latest telegram bot image
    echo "📥 Pulling latest Telegram bot image..."
    update_docker_images telegram
    
    # Stop existing telegram bot
    $DOCKER_CMD stop autotrader-telegram 2>/dev/null || true
    $DOCKER_CMD rm autotrader-telegram 2>/dev/null || true

    # Start telegram bot container with production image
    echo "🚀 Starting Telegram bot container..."
    $DOCKER_CMD run -d \
        --name autotrader-telegram \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -v /var/run/docker.sock:/var/run/docker.sock:ro \
        "$TELEGRAM_IMAGE"
    
    echo "✅ Telegram bot deployed successfully"
    echo "📱 Container: autotrader-telegram"
    echo "🖼️  Image: $TELEGRAM_IMAGE"
}

# Deploy trader bot infrastructure
deploy_trader_bot() {
    echo "🤖 Deploying Trader Bot Infrastructure"
    echo "====================================="
    
    if ! check_docker; then
        return 1
    fi
    
    # Pull latest trader image
    echo "📥 Pulling latest trader image..."
    update_docker_images trader
    
    echo "✅ Trader bot infrastructure ready"
    echo "🖼️  Image: $TRADER_IMAGE"
    echo "💡 Use 'telegram' or './bot.sh start <symbol>' to create trading bots"
}

# Start both telegram and trader systems
start_all() {
    echo "🚀 Starting Complete AutoTrader System"
    echo "======================================"
    
    # Start telegram bot in background
    echo "📱 Starting Telegram bot..."
    deploy_telegram_bot
    
    # Prepare trader infrastructure
    echo "🤖 Preparing trader infrastructure..."
    deploy_trader_bot
    
    echo "🎉 AutoTrader system ready!"
    echo ""
    echo "📋 Next steps:"
    echo "  • Use Telegram commands to manage trading bots"
    echo "  • Or use './bot.sh start <symbol>' for manual control"
    echo "  • Check status with './bot.sh status' or '/status' in Telegram"
    
    send_telegram_notification "🎉 AutoTrader system started successfully! Ready for trading operations."
}

# Stop all services
stop_all() {
    echo "⏹️ Stopping AutoTrader System"
    echo "============================="
    
    # Stop telegram bot
    echo "📱 Stopping Telegram bot..."
    $DOCKER_CMD stop autotrader-telegram 2>/dev/null || true
    $DOCKER_CMD rm autotrader-telegram 2>/dev/null || true

    # Stop all trading containers
    echo "🤖 Stopping all trading bots..."
    local containers=$($DOCKER_CMD ps --format "{{.Names}}" | grep -E "(crypto|trading|autotrader)" || true)
    
    if [[ -n "$containers" ]]; then
        echo "$containers" | while read container; do
            echo "  Stopping: $container"
            $DOCKER_CMD stop "$container" 2>/dev/null || true
            $DOCKER_CMD rm "$container" 2>/dev/null || true
        done
    fi
    
    echo "✅ All services stopped"
    send_telegram_notification "⏹️ AutoTrader system stopped. All trading operations halted."
}

# System status overview
system_status() {
    echo "📊 AutoTrader System Status"
    echo "=========================="

    # Initialize Docker command
    check_docker >/dev/null 2>&1 || true

    # Check telegram bot
    echo "📱 Telegram Bot:"
    if $DOCKER_CMD ps --format "{{.Names}}" 2>/dev/null | grep -q "autotrader-telegram"; then
        echo "   Status: ✅ Running (Docker)"
    elif ps aux 2>/dev/null | grep -q "[r]un_telegram_app.py"; then
        echo "   Status: ✅ Running (Local)"
    else
        echo "   Status: ❌ Stopped"
    fi

    # Check trading bots
    echo ""
    echo "🤖 Trading Bots:"
    local trading_containers=$($DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null | grep -E "(crypto|trading|autotrader)" | grep -v "autotrader-telegram" || true)
    
    if [[ -n "$trading_containers" ]]; then
        echo "$trading_containers"
    else
        echo "   No active trading bots"
    fi
    
    # Check Docker
    echo ""
    echo "🐳 Docker Status:"
    if check_docker; then
        echo "   Status: ✅ Available"
        local telegram_images=$($DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep "$IMAGE_BASE-telegram" | wc -l | tr -d ' ')
        local trader_images=$($DOCKER_CMD images --format "{{.Repository}}:{{.Tag}}" | grep "$IMAGE_BASE-trader" | wc -l | tr -d ' ')
        echo "   Telegram images: $telegram_images"
        echo "   Trader images: $trader_images"
        echo "   📱 Telegram: $TELEGRAM_IMAGE"
        echo "   🤖 Trader: $TRADER_IMAGE"
    else
        echo "   Status: ❌ Not available"
    fi
    
    # Check environment
    echo ""
    echo "🔧 Environment:"
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "   Telegram Token: ✅ Set (${TELEGRAM_BOT_TOKEN:0:10}...)"
    else
        echo "   Telegram Token: ❌ Not set"
    fi
    
    if [[ -n "$TELEGRAM_CHAT_ID" ]]; then
        echo "   Chat ID: ✅ Set ($TELEGRAM_CHAT_ID)"
    else
        echo "   Chat ID: ❌ Not set"
    fi
    
    echo "   Python venv: $([[ -f ".venv/bin/activate" ]] && echo "✅ Available" || echo "❌ Not found")"
}

# Stop all trading containers
stop_all_containers() {
    echo "⏹️ Stopping All Trading Containers"
    echo "=================================="

    # Get all autotrader containers except telegram
    local containers=$($DOCKER_CMD ps --format "{{.Names}}" | grep -E "(autotrader|trading|crypto)" | grep -v "autotrader-telegram" || true)

    if [[ -z "$containers" ]]; then
        echo "📭 No trading containers found"
        return 0
    fi

    echo "🔍 Found containers:"
    echo "$containers"
    echo ""

    # Stop each container
    local stopped_count=0
    while IFS= read -r container; do
        if [[ -n "$container" ]]; then
            echo "⏹️ Stopping: $container"
            if $DOCKER_CMD stop "$container" >/dev/null 2>&1; then
                echo "   ✅ Stopped successfully"
                ((stopped_count++))
            else
                echo "   ❌ Failed to stop"
            fi
        fi
    done <<< "$containers"

    echo ""
    echo "✅ Stopped $stopped_count containers"
}

# List configuration files
list_config_files() {
    echo "📁 Configuration Files"
    echo "======================"

    local configs_dir="configs"

    if [[ ! -d "$configs_dir" ]]; then
        echo "❌ Configs directory not found: $configs_dir"
        echo "💡 Create configs directory and add .json files"
        return 1
    fi

    # Find all .json files
    local config_files=$(find "$configs_dir" -name "*.json" -type f 2>/dev/null || true)

    if [[ -z "$config_files" ]]; then
        echo "📭 No configuration files found"
        echo "💡 Add .json config files to $configs_dir directory"
        return 0
    fi

    echo "✅ Found configuration files:"
    echo ""

    while IFS= read -r config_file; do
        if [[ -n "$config_file" ]]; then
            local filename=$(basename "$config_file")
            local size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "unknown")
            local modified=$(stat -f%Sm "$config_file" 2>/dev/null || stat -c%y "$config_file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")

            echo "  📄 $filename"
            echo "     Size: $size bytes"
            echo "     Modified: $modified"
            echo ""
        fi
    done <<< "$config_files"
}

# Show configuration file content
show_config_file() {
    local config_name="$1"

    if [[ -z "$config_name" ]]; then
        echo "❌ Usage: show-config <config_name>"
        return 1
    fi

    local config_file="configs/${config_name}.json"

    if [[ ! -f "$config_file" ]]; then
        echo "❌ Configuration file not found: $config_file"
        echo "💡 Use 'list-configs' to see available configurations"
        return 1
    fi

    echo "📄 Configuration: $config_name"
    echo "================================"
    echo ""

    # Show file info
    local size=$(stat -f%z "$config_file" 2>/dev/null || stat -c%s "$config_file" 2>/dev/null || echo "unknown")
    local modified=$(stat -f%Sm "$config_file" 2>/dev/null || stat -c%y "$config_file" 2>/dev/null | cut -d' ' -f1 || echo "unknown")

    echo "📁 File: $config_file"
    echo "📏 Size: $size bytes"
    echo "📅 Modified: $modified"
    echo ""
    echo "📄 Content:"
    echo "----------"

    # Pretty print JSON if possible
    if command -v jq >/dev/null 2>&1; then
        jq '.' "$config_file" 2>/dev/null || cat "$config_file"
    else
        cat "$config_file"
    fi
}

# Quick setup for new installations
quick_setup() {
    echo "⚡ AutoTrader Quick Setup"
    echo "========================"
    
    # Check requirements
    echo "🔍 Checking requirements..."
    
    local missing_reqs=()
    
    if ! command -v docker &> /dev/null; then
        missing_reqs+=("Docker")
    fi
    
    if ! command -v python3 &> /dev/null; then
        missing_reqs+=("Python3")
    fi
    
    if [[ ${#missing_reqs[@]} -gt 0 ]]; then
        echo "❌ Missing requirements: ${missing_reqs[*]}"
        echo "Please install missing requirements and run setup again"
        return 1
    fi
    
    # Setup virtual environment
    if [[ ! -f ".venv/bin/activate" ]]; then
        echo "🐍 Setting up Python virtual environment..."
        python3 -m venv .venv
    fi
    
    # Install dependencies
    echo "📦 Installing dependencies..."
    source .venv/bin/activate
    pip install -r requirements.txt
    
    # Pull Docker images
    echo "🐳 Pulling Docker images..."
    update_docker_images both
    
    # Check telegram setup
    echo "📱 Checking Telegram setup..."
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "⚠️ Telegram not configured. Set environment variables:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
    else
        echo "✅ Telegram configured"
    fi
    
    echo ""
    echo "🎉 Quick setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Configure Telegram (if not done): export TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID"
    echo "  2. Start system: './bot.sh start-all'"
    echo "  3. Or start individual components:"
    echo "     • './bot.sh telegram' - Start Telegram bot only"
    echo "     • './bot.sh start <symbol>' - Start trading bot only"
}

# Show help
show_help() {
    cat << EOF
🤖 AutoTrader Bot - Complete Trading System Controller

USAGE:
    $0 <command> [options]

SYSTEM COMMANDS:
    setup                      Quick setup for new installations
    start-all                  Start complete AutoTrader system (Telegram + Trader)
    stop-all                   Stop all AutoTrader services
    system-status             Show complete system status
    deploy <mode>             Deploy to server mode (telegram|trader|both)

TELEGRAM COMMANDS:
    telegram                  Start Telegram bot
    telegram-deploy           Deploy Telegram bot in Docker

TRADING COMMANDS:
    start <symbol> [options]  Start trading bot
    stop <symbol>            Stop trading bot
    restart <symbol>         Restart trading bot
    stop-all                 Stop all trading bots
    list                     List all containers
    status [symbol]          Show container status
    logs <symbol> [lines]    Show container logs

CREDENTIAL COMMANDS:
    list-credentials         List stored API credentials
    store-credentials        Store API credentials securely
    load-credentials         Load credentials into environment
    show-credentials         Show credential details (masked)

CONFIG COMMANDS:
    list-configs             List configuration files
    show-config <name>       Show configuration content

UTILITY COMMANDS:
    help                     Show this help
    version                  Show version information

START OPTIONS:
    --amount <amount>        Trading amount (default: 50)
    --test-mode             Enable test mode
    --direction <long|short> Trading direction
    --stop-loss <percent>    Stop loss percentage
    --take-profit <percent>  Take profit percentage
    --key <api_key>         API key (or use env BYBIT_API_KEY)
    --secret <api_secret>   API secret (or use env BYBIT_API_SECRET)

EXAMPLES:
    # Quick setup new installation
    $0 setup
    
    # Start complete system
    $0 start-all
    
    # Individual components
    $0 telegram                    # Start Telegram bot only
    $0 start hyper --amount 50     # Start HYPER trading bot
    
    # System management
    $0 system-status              # Check all services
    $0 stop-all                   # Stop everything
    
    # Server deployment
    $0 deploy both                # Deploy both services
    $0 deploy telegram            # Deploy only Telegram bot

TELEGRAM SETUP:
    export TELEGRAM_BOT_TOKEN="your_bot_token"
    export TELEGRAM_CHAT_ID="your_chat_id"

FEATURES:
    ✅ Complete system control via single bot.sh file
    ✅ Manual trading bot management on server
    ✅ Full remote control via Telegram
    ✅ Docker deployment for production
    ✅ Unified monitoring and status reporting

For detailed documentation, visit: https://github.com/your-repo/autotrader

EOF
}

# Show version
show_version() {
    echo "🤖 AutoTrader Bot System"
    echo "======================="
    echo "Version: $SCRIPT_VERSION"
    echo "Build: Production Ready"
    echo ""
    echo "Components:"
    echo "  • Trading Engine: Docker-based"
    echo "  • Telegram Bot: Python-based"
    echo "  • CLI Interface: Bash-based"
    echo ""
    echo "Requirements:"
    if check_docker >/dev/null 2>&1; then
        echo "  • Docker: $($DOCKER_CMD --version 2>/dev/null)"
    else
        echo "  • Docker: Not installed"
    fi
    echo "  • Python: $(python3 --version 2>/dev/null || echo "Not installed")"
    echo "  • Environment: $([[ -f ".venv/bin/activate" ]] && echo "Virtual env available" || echo "No virtual env")"
}

# Main function
main() {
    check_version_and_enforce_upgrade "$1"
    
    case "${1:-help}" in
        # System commands
        setup)
            quick_setup
            ;;
        start-all)
            start_all
            ;;
        stop-all)
            stop_all
            ;;
        system-status)
            system_status
            ;;
        deploy)
            deploy_server "$2"
            ;;
        
        # Telegram commands
        telegram)
            start_telegram_bot
            ;;
        telegram-deploy)
            deploy_telegram_bot
            ;;
        
        # Trading commands
        start)
            shift
            start_bot "$@"
            ;;
        stop)
            stop_bot "$2"
            ;;
        restart)
            restart_bot "$2"
            ;;
        list)
            list_containers
            ;;
        status)
            get_status "$2"
            ;;
        logs)
            get_logs "$2" "$3"
            ;;
        
        # Credential commands - Delegate to Python
        list-credentials)
            execute_python_command python3 run_telegram_app.py list-credentials
            ;;
        store-credentials)
            shift
            execute_python_command python3 run_telegram_app.py store-credentials "$@"
            ;;
        load-credentials)
            execute_python_command python3 run_telegram_app.py load-credentials "$2"
            ;;
        show-credentials)
            execute_python_command python3 run_telegram_app.py show-credentials "$2"
            ;;

        # Bot management commands
        stop-all)
            stop_all_containers
            ;;
        list-configs)
            list_config_files
            ;;
        show-config)
            show_config_file "$2"
            ;;

        # Utility commands
        version)
            show_version
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "❌ Unknown command: $1"
            echo ""
            echo "🔍 Did you mean:"
            echo "  • $0 setup          - Initial setup"
            echo "  • $0 start-all      - Start everything"
            echo "  • $0 system-status  - Check status"
            echo "  • $0 help           - Full help"
            exit 1
            ;;
    esac
}

# Execute main function
main "$@" 