#!/usr/bin/env python3
"""Improved Telegram Command Handler with comprehensive functionality."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from ..telegram_base import TelegramBaseHandler, UserSessionManager, ValidationUtils
from ..templates import TelegramTemplates
from ..trading_notifications import TradingNotificationManager


class ImprovedTelegramCommandHandler(TelegramBaseHandler):
    """Enhanced Telegram command handler with comprehensive bot management features."""
    
    def __init__(self, bot_token: str, chat_id: int):
        super().__init__('ImprovedTelegramCommandHandler')
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.application = None
        self.session_manager = UserSessionManager()
        self.bot_script_path = "/app/bot.sh"  # Path to bot.sh script

        # Initialize notification manager
        self.notification_manager = TradingNotificationManager(bot_token, chat_id)
    
    def start(self):
        """Start the Telegram bot with polling (non-async version)"""
        try:
            print("🔧 Creating Telegram application...")
            # Create application
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            # Add command handlers
            self.application.add_handler(CommandHandler("start", self.handle_start))
            self.application.add_handler(CommandHandler("help", self.handle_help))
            self.application.add_handler(CommandHandler("cancel", self.handle_cancel))

            # Credential commands
            self.application.add_handler(CommandHandler("addcreds", self.handle_addcreds_wizard))
            self.application.add_handler(CommandHandler("listcreds", self.handle_listcreds))
            self.application.add_handler(CommandHandler("loadcreds", self.handle_loadcreds))
            self.application.add_handler(CommandHandler("showcreds", self.handle_showcreds))
            self.application.add_handler(CommandHandler("setkey", self.handle_setkey))

            # Bot management commands
            self.application.add_handler(CommandHandler("createbot", self.handle_createbot_wizard))
            self.application.add_handler(CommandHandler("startbot", self.handle_startbot))
            self.application.add_handler(CommandHandler("list", self.handle_list))
            self.application.add_handler(CommandHandler("testlist", self.handle_testlist))
            self.application.add_handler(CommandHandler("testdocker", self.handle_testdocker))
            self.application.add_handler(CommandHandler("status", self.handle_status))
            self.application.add_handler(CommandHandler("logs", self.handle_logs))
            self.application.add_handler(CommandHandler("stop", self.handle_stopbot))
            self.application.add_handler(CommandHandler("restart", self.handle_restart))
            self.application.add_handler(CommandHandler("stopall", self.handle_stopall))

            # Config management commands
            self.application.add_handler(CommandHandler("createconfig", self.handle_createconfig))
            self.application.add_handler(CommandHandler("listconfigs", self.handle_listconfigs))
            self.application.add_handler(CommandHandler("showconfig", self.handle_showconfig))

            # Notification commands
            self.application.add_handler(CommandHandler("subscribe", self.handle_subscribe))
            self.application.add_handler(CommandHandler("unsubscribe", self.handle_unsubscribe))
            self.application.add_handler(CommandHandler("alerts", self.handle_alerts))
            self.application.add_handler(CommandHandler("testnotify", self.handle_test_notification))

            # Callback query handler for wizards
            self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

            # Message handler for wizard inputs
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_wizard_message))

            # Add error handler
            self.application.add_error_handler(self.handle_error)

            # Run the bot - this blocks until stopped
            print("🚀 Starting bot polling...")
            self.logger.info("Starting Telegram bot polling...")
            self.application.run_polling(allowed_updates=Update.ALL_TYPES)

        except Exception as e:
            print(f"❌ Error starting Telegram bot: {e}")
            self.logger.error(f"Error starting Telegram bot: {e}")
            raise
    
    async def handle_error(self, update: object, context) -> None:
        """Handle errors that occur during bot operation"""
        self.logger.error(f"Telegram bot error: {context.error}")
        
        # Try to send error message to user if possible
        try:
            if update and hasattr(update, 'effective_chat') and update.effective_chat:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ **Xảy ra lỗi khi xử lý lệnh**\n\nVui lòng thử lại sau.",
                    parse_mode=ParseMode.MARKDOWN
                )
        except Exception as e:
            self.logger.error(f"Failed to send error message: {e}")
    
    def escape_html(self, text: str) -> str:
        """Escape HTML special characters in text"""
        if not text:
            return ""
        return (text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace('"', "&quot;")
                   .replace("'", "&#x27;"))
    
    async def stop(self):
        """Stop the Telegram bot"""
        if hasattr(self, 'application') and self.application:
            try:
                await self.application.stop()
                await self.application.shutdown()
            except Exception as e:
                self.logger.error(f"Error stopping application: {e}")
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by base class"""
        # This is handled by individual command handlers
        pass

    # Basic Commands
    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command"""
        self.logger.info(f"Received /start command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        user = update.effective_user
        welcome_text = f"""
🤖 **AutoTrader Bot** chào mừng {user.first_name}!

Tôi là bot quản lý giao dịch tự động của bạn.

**Lệnh cơ bản:**
• `/help` - Xem tất cả lệnh
• `/addcreds` - Thêm thông tin tài khoản
• `/createbot` - Tạo bot trading

Hãy bắt đầu bằng cách thêm thông tin tài khoản với `/addcreds`!
        """

        keyboard = [
            [InlineKeyboardButton("📖 Hướng dẫn", callback_data="help_guide")],
            [InlineKeyboardButton("⚙️ Thêm tài khoản", callback_data="start_addcreds")],
            [InlineKeyboardButton("🤖 Tạo bot", callback_data="start_createbot")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            welcome_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command"""
        help_text = """
🤖 **AutoTrader Bot - Hướng dẫn sử dụng**

**📋 Quản lý tài khoản:**
• `/addcreds` - Thêm thông tin tài khoản exchange
• `/listcreds` - Xem danh sách tài khoản
• `/loadcreds <profile>` - Tải profile tài khoản
• `/setkey <key> <secret>` - Lưu key/secret nhanh

**🤖 Quản lý bot:**
• `/createbot` - Tạo bot mới (wizard)
• `/startbot <symbol> <amount>` - Khởi động nhanh
• `/list` - Xem tất cả bot
• `/status <symbol>` - Trạng thái bot
• `/logs <symbol> [lines]` - Xem log
• `/stop <symbol>` - Dừng bot
• `/restart <symbol>` - Khởi động lại
• `/stopall` - Dừng tất cả bot

**⚙️ Config & Notifications:**
• `/listconfigs` - Xem danh sách config
• `/showconfig <name>` - Xem nội dung config
• `/subscribe` - Đăng ký thông báo
• `/unsubscribe` - Hủy thông báo
• `/alerts` - Cấu hình cảnh báo
• `/testnotify [type]` - Test thông báo

**Ví dụ:**
• `/setkey abc123 xyz789` - Lưu API key
• `/startbot BTCUSDT 100` - Trade BTC với $100
• `/logs BTCUSDT 20` - Xem 20 dòng log mới nhất
• `/cancel` - Hủy wizard hiện tại

💡 Sử dụng `/createbot` để setup chi tiết hơn!
        """
        
        # Check if this is from callback query or regular message
        if update.callback_query:
            # From callback query (button click)
            query = update.callback_query
            await query.edit_message_text(help_text, parse_mode=ParseMode.MARKDOWN)
        else:
            # From regular message command
            await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

    async def handle_cancel(self, update: Update, context) -> None:
        """Handle /cancel command - cancel current wizard"""
        user_id = update.effective_user.id

        session = self.session_manager.get_session(user_id)
        if not session or not session.get('wizard_state'):
            await update.message.reply_text(
                "❌ <b>Không có thao tác nào để hủy</b>\n\n"
                "Bạn không đang trong quá trình thực hiện wizard nào.",
                parse_mode=ParseMode.MARKDOWN
            )
            return

        wizard_type = session['wizard_state']
        self.session_manager.clear_session(user_id)

        await update.message.reply_text(
            f"✅ **Đã hủy {wizard_type}**\n\n"
            "Bạn có thể bắt đầu lại bất cứ lúc nào.",
            parse_mode=ParseMode.MARKDOWN
        )

    # Credential Management Commands
    async def handle_setkey(self, update: Update, context) -> None:
        """Handle /setkey command"""
        chat_id = update.effective_chat.id
        
        # Get arguments
        args = context.args if context.args else []
        
        if len(args) != 2:
            help_text = """
🔐 **Lưu API Key nhanh**

Cú pháp: `/setkey <api_key> <api_secret>`

Ví dụ:
• `/setkey abc123... xyz789...`

💡 Lệnh này lưu key vào profile "default"
Sử dụng `/addcreds` để tạo nhiều profile khác nhau.
            """
            await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)
            return
        
        api_key, api_secret = args[0], args[1]
        
        # Validate inputs
        if not ValidationUtils.validate_api_key(api_key):
            await update.message.reply_text(
                "❌ **API key không hợp lệ**\n\nKey phải có ít nhất 10 ký tự.",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        # Store credentials using bot.sh
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "store-credentials", "default", api_key, api_secret, "Default Account"
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    "✅ **Lưu thành công!**\n\n"
                    "API key đã được lưu vào profile 'default'.\n"
                    "Bây giờ bạn có thể tạo bot với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(
                    f"❌ **Lỗi lưu key:** ```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_listcreds(self, update: Update, context) -> None:
        """Handle /listcreds command"""
        self.logger.info(f"Received /listcreds command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        chat_id = update.effective_chat.id

        try:
            # Get credentials list using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list-credentials"])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📋 **Danh sách tài khoản:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(
                    f"❌ **Lỗi:** ```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_loadcreds(self, update: Update, context) -> None:
        """Handle /loadcreds command"""
        chat_id = update.effective_chat.id
        
        # Get profile argument
        profile = context.args[0] if context.args else "default"
        
        try:
            # Load credentials using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "load-credentials", profile])
            
            if result[0] == 0:
                success_msg = f"✅ **Tải thành công!**\n\n"
                success_msg += f"Profile: `{profile}`\n\n"
                success_msg += "**Sẵn sàng tạo bot:**\n"
                success_msg += "• `/createbot` - Tạo bot với wizard\n"
                success_msg += "• `/startbot symbol amount` - Khởi động nhanh\n"
                success_msg += "• `/list` - Xem tất cả bot"

                await update.message.reply_text(success_msg, parse_mode=ParseMode.MARKDOWN)
            else:
                await update.message.reply_text(
                    f"❌ **Lỗi tải profile '{profile}':**\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_showcreds(self, update: Update, context) -> None:
        """Handle /showcreds command"""
        self.logger.info(f"Received /showcreds command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        # Get profile name from arguments or use default
        profile = context.args[0] if context.args else "default"

        try:
            # Show credentials using bot.sh (this will need to be implemented)
            result = await self._execute_botsh_command([self.bot_script_path, "show-credentials", profile])

            if result[0] == 0:
                await update.message.reply_text(
                    f"🔐 <b>Thông tin tài khoản '{self.escape_html(profile)}':</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi hiển thị profile '{self.escape_html(profile)}':</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_addcreds_wizard(self, update: Update, context) -> None:
        """Handle /addcreds command - start credentials wizard"""
        user_id = update.effective_user.id
        
        self.session_manager.start_wizard(user_id, 'addcreds', {
            'step': 'profile_name',
            'data': {}
        })
        
        message_text = (
            "🔐 **Thêm thông tin tài khoản**\n\n"
            "Bước 1/4: Nhập tên profile (ví dụ: bybit_main, binance_test)\n\n"
            "💡 Tên profile giúp bạn phân biệt các tài khoản khác nhau.\n\n"
            "📝 Nhập tên profile hoặc `/cancel` để hủy:"
        )
        
        # Check if this is from callback query or regular message
        if update.callback_query:
            # From callback query (button click)
            query = update.callback_query
            await query.edit_message_text(message_text, parse_mode=ParseMode.MARKDOWN)
            # Send a new message for input since edited messages can't have reply_markup
            await query.message.reply_text(
                "👆 Nhập tên profile:",
                reply_markup=ForceReply(selective=True)
            )
        else:
            # From regular message command
            await update.message.reply_text(
                message_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=ForceReply(selective=True)
            )

    # Bot Management Commands
    async def handle_createbot_wizard(self, update: Update, context) -> None:
        """Handle /createbot command - start createbot wizard"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id

        # Clear any existing wizard
        self.session_manager.clear_session(user_id)

        # Check prerequisites
        if not await self._check_prerequisites():
            error_text = (
                "❌ **Không đủ điều kiện tạo bot**\n\n"
                "Vui lòng:\n"
                "1. Thêm thông tin tài khoản với `/addcreds` hoặc `/setkey`\n"
                "2. Đảm bảo Docker đang chạy\n\n"
                "Thử lại sau khi hoàn thành các bước trên."
            )

            if update.callback_query:
                query = update.callback_query
                await query.edit_message_text(error_text, parse_mode=ParseMode.MARKDOWN)
            else:
                await update.message.reply_text(error_text, parse_mode=ParseMode.MARKDOWN)
            return

        # Start wizard with credentials selection
        await self._start_createbot_wizard(chat_id, context)

    async def handle_startbot(self, update: Update, context) -> None:
        """Handle /startbot command"""
        chat_id = update.effective_chat.id
        
        if len(context.args) < 2:
            usage_msg = """
🚀 **Khởi động bot nhanh**

Cú pháp: `/startbot <symbol> <amount> [options]`

**Ví dụ:**
• `/startbot BTCUSDT 50` - Trade BTC với $50
• `/startbot ETHUSDT 100 test` - Test mode với $100
• `/startbot HYPER 25` - Trade HYPER với $25

💡 Sử dụng `/createbot` để setup chi tiết hơn.
            """

            await update.message.reply_text(usage_msg, parse_mode=ParseMode.MARKDOWN)
            return
        
        symbol = context.args[0]
        amount = context.args[1]
        
        # Validate inputs
        if not ValidationUtils.validate_amount(amount):
            await update.message.reply_text(
                "❌ **Số tiền không hợp lệ**\n\nVui lòng nhập số dương.",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        try:
            # Build command arguments
            cmd_args = [self.bot_script_path, "start", symbol, "--amount", amount]
            
            # Add additional arguments
            for arg in context.args[2:]:
                if arg.lower() in ["test", "testmode", "test_mode"]:
                    cmd_args.append("--test-mode")
                else:
                    cmd_args.append(arg)
            
            # Start bot using bot.sh
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🚀 **Bot đã khởi động!**\n\n"
                    f"Symbol: `{symbol}`\n"
                    f"Amount: `${amount}`\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(
                    f"❌ **Lỗi khởi động:**\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_testlist(self, update: Update, context) -> None:
        """Handle /testlist command for debugging"""
        try:
            # Test with sample data
            sample_output = """📊 Trading Bot Containers
=========================
🐳 Running inside Docker container
autotrader-telegram    Up About an hour (unhealthy)    autotrader-telegram-manager
2025-07-14 07:33:26 +0000 UTC"""

            formatted_info = await self._format_bot_list(sample_output)
            await update.message.reply_text(
                f"🧪 **Test List Output:**\n\n{formatted_info}",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ **Test Error:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_testdocker(self, update: Update, context) -> None:
        """Handle /testdocker command for debugging Docker access"""
        try:
            # Test bot.sh first
            result = await self._execute_botsh_command([self.bot_script_path, "help"])

            if result[0] != 0:
                await update.message.reply_text(
                    f"❌ **bot.sh không hoạt động:**\n\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Test Docker access
            result = await self._execute_botsh_command(["docker", "version"])

            if result[0] == 0:
                await update.message.reply_text(
                    f"✅ **Docker Access OK**\n\n```\n{result[1][:500]}...\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await update.message.reply_text(
                    f"❌ **Docker Access Failed**\n\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ **Test Docker Error:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_list(self, update: Update, context) -> None:
        """Handle /list command"""
        try:
            # Get bot status using bot.sh list command
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] == 0:
                if result[1].strip():
                    # Parse and format the bot information
                    formatted_info = await self._format_bot_list(result[1])

                    # If no bots found, show a helpful message
                    if "Không có bot trading nào đang chạy" in formatted_info:
                        await update.message.reply_text(
                            "📋 **Không có bot trading nào đang chạy**\n\n"
                            "Chỉ có Telegram bot container đang hoạt động.\n\n"
                            "Sử dụng `/createbot` để tạo bot trading mới.",
                            parse_mode=ParseMode.MARKDOWN
                        )
                    else:
                        await update.message.reply_text(
                            f"📋 **Danh sách bot đang hoạt động:**\n\n{formatted_info}",
                            parse_mode=ParseMode.MARKDOWN
                        )
                else:
                    await update.message.reply_text(
                        "📋 **Không có bot nào đang chạy**\n\n"
                        "Sử dụng `/createbot` để tạo bot mới.",
                        parse_mode=ParseMode.MARKDOWN
                    )
            else:
                # Handle Docker daemon connection error specifically
                error_msg = result[2] or result[1]
                if "Cannot connect to Docker daemon" in error_msg:
                    await update.message.reply_text(
                        "⚠️ **Không thể kết nối Docker daemon**\n\n"
                        "Container cần được restart với Docker socket access.\n\n"
                        "Vui lòng liên hệ admin để khắc phục.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await update.message.reply_text(
                        f"❌ **Lỗi:** ```\n{error_msg}\n```",
                        parse_mode=ParseMode.MARKDOWN
                    )

        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )
    
    async def handle_status(self, update: Update, context) -> None:
        """Handle /status command"""
        if not context.args:
            await update.message.reply_text(
                "📊 <b>Xem trạng thái bot</b>\n\n"
                "Cú pháp: <code>/status &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/status BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        try:
            # Get status using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "status", symbol])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 <b>Trạng thái {self.escape_html(symbol)}:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Không tìm thấy bot cho symbol '{self.escape_html(symbol)}'</b>\n\n"
                    "Sử dụng <code>/list</code> để xem tất cả bot.",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_logs(self, update: Update, context) -> None:
        """Handle /logs command"""
        if not context.args:
            await update.message.reply_text(
                "📜 <b>Xem log bot</b>\n\n"
                "Cú pháp: <code>/logs &lt;symbol&gt; [lines]</code>\n\n"
                "Ví dụ:\n"
                "• <code>/logs BTCUSDT</code> - 50 dòng cuối\n"
                "• <code>/logs BTCUSDT 20</code> - 20 dòng cuối",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        lines = context.args[1] if len(context.args) > 1 else "50"
        
        try:
            # Get logs using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "logs", symbol, lines])
            
            if result[0] == 0:
                if result[1].strip():
                                    await update.message.reply_text(
                    f"📜 <b>Log {self.escape_html(symbol)} ({lines} dòng):</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                else:
                    await update.message.reply_text(
                        f"📜 <b>Không có log cho <code>{self.escape_html(symbol)}</code></b>",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await update.message.reply_text(
                    f"❌ <b>Không tìm thấy bot cho symbol '<code>{self.escape_html(symbol)}</code>'</b>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_stopbot(self, update: Update, context) -> None:
        """Handle /stop command"""
        if not context.args:
            await update.message.reply_text(
                "⏹️ <b>Dừng bot</b>\n\n"
                "Cú pháp: <code>/stop &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/stop BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        # Send confirmation request
        keyboard = [
            [InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
             InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"⏹️ <b>Xác nhận dừng bot</b>\n\n"
            f"Bạn có chắc muốn dừng bot <code>{self.escape_html(symbol)}</code>?\n\n"
            "⚠️ Điều này sẽ dừng tất cả hoạt động giao dịch.",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
    
    async def handle_restart(self, update: Update, context) -> None:
        """Handle /restart command"""
        if not context.args:
            await update.message.reply_text(
                "🔄 <b>Khởi động lại bot</b>\n\n"
                "Cú pháp: <code>/restart &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/restart BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        try:
            # Restart using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "restart", symbol])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🔄 <b>Bot đã khởi động lại!</b>\n\n"
                    f"Symbol: <code>{self.escape_html(symbol)}</code>\n\n"
                    f"Sử dụng <code>/status {self.escape_html(symbol)}</code> để xem trạng thái.",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi khởi động lại:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_stopall(self, update: Update, context) -> None:
        """Handle /stopall command"""
        # Create confirmation keyboard
        keyboard = [
            [InlineKeyboardButton("✅ Xác nhận", callback_data="confirm_stopall")],
            [InlineKeyboardButton("❌ Hủy", callback_data="cancel_stopall")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "⚠️ <b>Dừng tất cả bot</b>\n\n"
            "Bạn có chắc chắn muốn dừng TẤT CẢ bot đang chạy?\n\n"
            "⚠️ Điều này sẽ dừng tất cả hoạt động giao dịch.",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

    # Config Management Commands
    async def handle_createconfig(self, update: Update, context) -> None:
        """Handle /createconfig command"""
        await update.message.reply_text(
            "⚙️ <b>Tạo cấu hình mới</b>\n\n"
            "Tính năng này đang được phát triển.\n"
            "Hiện tại bạn có thể sử dụng file config trong thư mục `configs/`",
            parse_mode=ParseMode.HTML
        )

    async def handle_listconfigs(self, update: Update, context) -> None:
        """Handle /listconfigs command"""
        try:
            # List config files using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list-configs"])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📁 <b>Danh sách cấu hình:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_showconfig(self, update: Update, context) -> None:
        """Handle /showconfig command"""
        if not context.args:
            await update.message.reply_text(
                "📄 <b>Xem cấu hình</b>\n\n"
                "Cú pháp: <code>/showconfig &lt;config_name&gt;</code>\n\n"
                "Ví dụ: <code>/showconfig default</code>",
                parse_mode=ParseMode.HTML
            )
            return

        config_name = context.args[0]

        try:
            # Show config using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "show-config", config_name])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📄 <b>Cấu hình '{self.escape_html(config_name)}':</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    # Notification Commands
    async def handle_subscribe(self, update: Update, context) -> None:
        """Handle /subscribe command"""
        chat_id = update.effective_chat.id

        # Simple subscription toggle
        await update.message.reply_text(
            "🔔 <b>Đăng ký thông báo</b>\n\n"
            "✅ Bạn đã đăng ký nhận thông báo:\n"
            "• Thông báo giao dịch\n"
            "• Cảnh báo lỗi\n"
            "• Cập nhật trạng thái bot\n\n"
            "Sử dụng <code>/unsubscribe</code> để hủy đăng ký.",
            parse_mode=ParseMode.HTML
        )

    async def handle_unsubscribe(self, update: Update, context) -> None:
        """Handle /unsubscribe command"""
        chat_id = update.effective_chat.id

        await update.message.reply_text(
            "🔕 <b>Hủy đăng ký thông báo</b>\n\n"
            "❌ Bạn đã hủy đăng ký thông báo.\n\n"
            "Sử dụng <code>/subscribe</code> để đăng ký lại.",
            parse_mode=ParseMode.HTML
        )

    async def handle_alerts(self, update: Update, context) -> None:
        """Handle /alerts command"""
        await update.message.reply_text(
            "⚠️ <b>Cấu hình cảnh báo</b>\n\n"
            "Tính năng này đang được phát triển.\n\n"
            "Hiện tại bot sẽ tự động gửi thông báo khi:\n"
            "• Có giao dịch mới\n"
            "• Bot gặp lỗi\n"
            "• Thay đổi trạng thái bot",
            parse_mode=ParseMode.HTML
        )

    async def handle_test_notification(self, update: Update, context) -> None:
        """Handle /testnotify command - test trading notifications"""
        try:
            # Initialize notification manager if not done
            if not hasattr(self.notification_manager, 'bot') or not self.notification_manager.bot:
                await self.notification_manager.initialize()

            # Test different types of notifications
            test_type = context.args[0] if context.args else "trade"

            if test_type == "trade":
                await self.notification_manager.send_trade_notification({
                    'symbol': 'BTC/USDT',
                    'side': 'BUY',
                    'amount': 100.0,
                    'price': 45000.0,
                    'timestamp': '14:30:25'
                })
                await update.message.reply_text("✅ Test trade notification sent!")

            elif test_type == "position":
                await self.notification_manager.send_position_update({
                    'symbol': 'ETH/USDT',
                    'side': 'LONG',
                    'size': 250.0,
                    'unrealized_pnl': 15.50,
                    'pnl_percentage': 2.3
                })
                await update.message.reply_text("✅ Test position notification sent!")

            elif test_type == "error":
                await self.notification_manager.send_error_alert({
                    'type': 'Connection Error',
                    'message': 'Failed to connect to exchange API',
                    'bot_name': 'BTC-USDT Bot',
                    'timestamp': '14:30:25'
                })
                await update.message.reply_text("✅ Test error notification sent!")

            elif test_type == "profit":
                await self.notification_manager.send_profit_alert({
                    'profit_amount': 125.50,
                    'profit_percentage': 8.5,
                    'total_trades': 15,
                    'win_rate': 73.3
                })
                await update.message.reply_text("✅ Test profit notification sent!")

            else:
                await update.message.reply_text(
                    "🧪 <b>Test Notifications</b>\n\n"
                    "Usage: <code>/testnotify [type]</code>\n\n"
                    "Types:\n"
                    "• <code>trade</code> - Trade execution\n"
                    "• <code>position</code> - Position update\n"
                    "• <code>error</code> - Error alert\n"
                    "• <code>profit</code> - Profit milestone",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi test notification:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    # Callback Query Handler
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle inline keyboard callbacks"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        user_id = query.from_user.id
        chat_id = query.message.chat.id
        
        if data == "help_guide":
            await self.handle_help(update, context)
        elif data == "start_addcreds":
            await self.handle_addcreds_wizard(update, context)
        elif data == "start_createbot":
            await self.handle_createbot_wizard(update, context)
        elif data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self._handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ **Đã hủy dừng bot**", parse_mode=ParseMode.MARKDOWN)
        elif data == "confirm_stopall":
            await self._handle_stopall_confirm(query)
        elif data == "cancel_stopall":
            await query.edit_message_text("❌ **Đã hủy dừng tất cả bot**", parse_mode=ParseMode.MARKDOWN)
        elif data == "createbot_confirm":
            await self._handle_createbot_confirm(query)
        elif data == "createbot_cancel":
            await self._handle_createbot_cancel(query)
        elif data.startswith("createbot_cred_"):
            profile = data.replace("createbot_cred_", "")
            await self._handle_createbot_credential_selected(query, profile)
        elif data.startswith("createbot_symbol_"):
            symbol = data.replace("createbot_symbol_", "")
            await self._handle_createbot_symbol_selected(query, symbol)
        elif data == "createbot_final_confirm":
            await self._handle_createbot_final_confirm(query)
        elif data == "createbot_edit":
            await self._handle_createbot_edit(query)
        else:
            await query.edit_message_text("❌ **Lệnh không xác định**", parse_mode=ParseMode.MARKDOWN)

    async def _handle_stop_confirm(self, query, symbol: str):
        """Handle stop confirmation"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "stop", symbol])
            
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ <b>Bot đã dừng!</b>\n\n"
                    f"Symbol: <code>{self.escape_html(symbol)}</code>\n\n"
                    f"Sử dụng <code>/startbot {self.escape_html(symbol)} &lt;amount&gt;</code> để khởi động lại.",
                    parse_mode=ParseMode.HTML
                )
            else:
                await query.edit_message_text(
                    f"❌ <b>Lỗi dừng bot:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await query.edit_message_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_confirm(self, query):
        """Handle stop all confirmation"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "stop-all"])

            if result[0] == 0:
                await query.edit_message_text(
                    f"⏹️ **Đã dừng tất cả bot**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi dừng tất cả bot:** ```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )

        except Exception as e:
            await query.edit_message_text(
                f"❌ **Lỗi dừng tất cả bot:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_confirm(self, query):
        """Handle createbot confirmation"""
        try:
            user_id = query.from_user.id
            session = self.session_manager.get_session(user_id)

            if not session or session.get('wizard_state') != 'createbot_confirm':
                await query.edit_message_text(
                    "❌ **Phiên làm việc đã hết hạn**\n\nVui lòng thử lại với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            data = session.get('wizard_data', {})
            symbol = data.get('symbol')
            amount = data.get('amount')

            if not symbol or not amount:
                await query.edit_message_text(
                    "❌ **Thiếu thông tin**\n\nVui lòng thử lại với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Execute bot creation
            result = await self._execute_botsh_command([
                self.bot_script_path, "start", symbol, amount
            ])

            if result[0] == 0:
                await query.edit_message_text(
                    f"🚀 **Bot đã được tạo thành công!**\n\n"
                    f"Symbol: `{symbol}`\n"
                    f"Amount: `${amount}`\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi tạo bot:**\n```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )

            # Clear session
            self.session_manager.clear_session(user_id)

        except Exception as e:
            await query.edit_message_text(
                f"❌ **Lỗi tạo bot:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    # Message Handler for Wizards
    async def handle_wizard_message(self, update: Update, context) -> None:
        """Handle wizard input messages"""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            # No active wizard, ignore
            return
        
        session = self.session_manager.get_session(user_id)
        wizard_type = session['wizard_state']

        # Check for cancel command
        if update.message.text.strip().lower() == '/cancel':
            await self.handle_cancel(update, context)
            return

        if wizard_type == 'addcreds':
            await self._handle_addcreds_step(update, context, session)
        elif wizard_type == 'createbot':
            await self._handle_createbot_step(update, context, session)

    async def _handle_addcreds_step(self, update: Update, context, session: Dict) -> None:
        """Handle steps in addcreds wizard"""
        user_id = update.effective_user.id
        step = session['wizard_data']['step']
        data = session['wizard_data']['data']
        text = update.message.text.strip()
        
        if step == 'profile_name':
            if not ValidationUtils.validate_profile_name(text):
                await update.message.reply_text(
                    "❌ **Tên profile không hợp lệ**\n\n"
                    "Tên chỉ được chứa chữ cái, số và dấu gạch dưới.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            data['profile'] = text
            self.session_manager.update_wizard_data(user_id, 'profile', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'api_key')
            
            await update.message.reply_text(
                "🔑 **Bước 2/4: Nhập API Key**\n\n"
                "Dán API Key từ exchange của bạn:\n\n"
                "📝 Nhập API Key hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'api_key':
            if not ValidationUtils.validate_api_key(text):
                await update.message.reply_text(
                    "❌ <b>API Key không hợp lệ</b>\n\n"
                    "Key phải có ít nhất 10 ký tự.",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['api_key'] = text
            self.session_manager.update_wizard_data(user_id, 'api_key', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'api_secret')
            
            await update.message.reply_text(
                "🔐 <b>Bước 3/4: Nhập API Secret</b>\n\n"
                "Dán API Secret từ exchange của bạn:\n\n"
                "📝 Nhập API Secret hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'api_secret':
            if not ValidationUtils.validate_api_key(text):  # Same validation as API key
                await update.message.reply_text(
                    "❌ <b>API Secret không hợp lệ</b>\n\n"
                    "Secret phải có ít nhất 10 ký tự.",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['api_secret'] = text
            self.session_manager.update_wizard_data(user_id, 'api_secret', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'description')
            
            await update.message.reply_text(
                "📝 <b>Bước 4/4: Nhập mô tả (tùy chọn)</b>\n\n"
                "Mô tả ngắn cho tài khoản này, hoặc gửi 'skip' để bỏ qua:\n\n"
                "📝 Nhập mô tả, 'skip' để bỏ qua, hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'description':
            if text.lower() == 'skip':
                data['description'] = f"Profile {data['profile']}"
            else:
                data['description'] = text
            
            # Save credentials
            await self._save_credentials(update, data)
            self.session_manager.clear_session(user_id)

    async def _save_credentials(self, update: Update, data: Dict):
        """Save credentials using bot.sh"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "store-credentials", 
                data['profile'], data['api_key'], data['api_secret'], data['description']
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"✅ <b>Lưu thành công!</b>\n\n"
                    f"Profile: <code>{self.escape_html(data['profile'])}</code>\n"
                    f"Mô tả: {self.escape_html(data['description'])}\n\n"
                    "Bây giờ bạn có thể tạo bot với <code>/createbot</code>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi lưu credentials:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ **Lỗi:** ```\n{str(e)}\n```",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_step(self, update: Update, context, session: Dict) -> None:
        """Handle steps in createbot wizard"""
        user_id = update.effective_user.id
        step = session['wizard_data']['step']
        data = session['wizard_data']['data']
        text = update.message.text.strip()
        
        if step == 'symbol':
            if not ValidationUtils.validate_symbol(text):
                await update.message.reply_text(
                    "❌ **Symbol không hợp lệ**\n\n"
                    "Ví dụ: BTCUSDT, ETHUSDT, HYPER",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            data['symbol'] = text.upper()
            session['wizard_data']['step'] = 'amount'
            self.session_manager.update_wizard_data(user_id, 'step', 'amount')
            
            await update.message.reply_text(
                f"💰 **Bước 2/3: Nhập số tiền trade**\n\n"
                f"Symbol: `{data['symbol']}`\n\n"
                "Nhập số tiền (USD) để trade:\n\n"
                "📝 Nhập số tiền hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'amount':
            if not ValidationUtils.validate_amount(text):
                await update.message.reply_text(
                    "❌ **Số tiền không hợp lệ**\n\n"
                    "Vui lòng nhập số dương (ví dụ: 100, 50.5)",
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            
            data['amount'] = text
            session['wizard_data']['step'] = 'confirm'
            self.session_manager.update_wizard_data(user_id, 'step', 'confirm')
            
            # Show confirmation
            keyboard = [
                [InlineKeyboardButton("✅ Xác nhận", callback_data="createbot_confirm"),
                 InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"🤖 **Bước 3/3: Xác nhận tạo bot**\n\n"
                f"Symbol: `{data['symbol']}`\n"
                f"Amount: `${data['amount']}`\n\n"
                "Xác nhận tạo bot?",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def _format_bot_list(self, raw_output: str) -> str:
        """Format bot list output for better display"""
        try:
            lines = raw_output.strip().split('\n')
            formatted_bots = []

            self.logger.info(f"Raw output lines: {lines}")

            for line in lines:
                if line.strip() and not line.startswith('=') and not line.startswith('Trading Bot') and not line.startswith('📊'):
                    self.logger.info(f"Processing line: {line}")
                    # Parse bot information from the line
                    bot_info = self._parse_bot_line(line)
                    if bot_info:
                        self.logger.info(f"Bot info found: {bot_info}")
                        # Try to get more detailed info for this bot
                        enhanced_info = await self._enhance_bot_info(bot_info, line)
                        formatted_bots.append(enhanced_info)
                    else:
                        self.logger.info(f"No bot info extracted from line: {line}")

            if not formatted_bots:
                self.logger.warning("No formatted bots found, returning raw output")
                return f"Không có bot trading nào đang chạy.\n\n**Debug info:**\n```\n{raw_output}\n```"

            return '\n\n'.join(formatted_bots)

        except Exception as e:
            self.logger.error(f"Error formatting bot list: {e}")
            return f"```\n{raw_output}\n```"

    async def _enhance_bot_info(self, basic_info: str, container_line: str) -> str:
        """Enhance bot info with more details"""
        try:
            # Extract container name from the line
            parts = container_line.split('\t') if '\t' in container_line else container_line.split()
            if not parts or 'autotrader' not in parts[0].lower():
                return basic_info

            container_name = parts[0]

            # Extract symbol from container name for status check
            symbol = "unknown"
            if 'autotrader-' in container_name.lower():
                symbol_part = container_name.split('-')[-1]
                if symbol_part and symbol_part != 'telegram':
                    symbol = symbol_part.lower()

            # Try to get detailed status
            if symbol != "unknown":
                status_result = await self._execute_botsh_command([self.bot_script_path, "status", symbol])
                if status_result[0] == 0 and status_result[1].strip():
                    # Parse status output for more details
                    status_lines = status_result[1].strip().split('\n')

                    amount = "Unknown"
                    credential = "default"
                    dca_amount = "Unknown"

                    for status_line in status_lines:
                        if 'amount' in status_line.lower() or '$' in status_line:
                            # Try to extract amount
                            import re
                            amount_match = re.search(r'\$?(\d+(?:\.\d+)?)', status_line)
                            if amount_match:
                                amount = f"${amount_match.group(1)}"
                        elif 'dca' in status_line.lower():
                            # Try to extract DCA amount
                            import re
                            dca_match = re.search(r'\$?(\d+(?:\.\d+)?)', status_line)
                            if dca_match:
                                dca_amount = f"${dca_match.group(1)}"
                        elif 'credential' in status_line.lower() or 'profile' in status_line.lower():
                            # Try to extract credential
                            cred_parts = status_line.split(':')
                            if len(cred_parts) > 1:
                                credential = cred_parts[1].strip()

                    # Update the basic info with enhanced details
                    enhanced_info = basic_info.replace('`Đang lấy thông tin...`', f'`{amount}`')
                    enhanced_info = enhanced_info.replace('`default`', f'`{credential}`')

                    # Add DCA amount if found
                    if dca_amount != "Unknown":
                        enhanced_info += f"\n💰 DCA Amount: `{dca_amount}`"

                    return enhanced_info

            return basic_info

        except Exception as e:
            self.logger.error(f"Error enhancing bot info: {e}")
            return basic_info

    def _parse_bot_line(self, line: str) -> str:
        """Parse a single bot line and format it"""
        try:
            # Skip header lines and empty lines
            if not line.strip() or 'NAMES' in line or 'STATUS' in line or '=' in line or 'Trading Bot' in line or 'Version check' in line or '📊' in line or '🐳' in line:
                return None

            self.logger.info(f"Parsing line: '{line}'")

            # Parse container information
            # Expected format: "container_name    status    image    created_time"
            parts = line.split('\t') if '\t' in line else line.split()

            self.logger.info(f"Line parts: {parts}")

            if len(parts) >= 2 and 'autotrader' in parts[0].lower():
                container_name = parts[0]
                status_info = ' '.join(parts[1:]) if len(parts) > 1 else "Unknown"

                self.logger.info(f"Container: {container_name}, Status: {status_info}")

                # Extract symbol from container name
                symbol = "Unknown"
                if 'autotrader-' in container_name.lower():
                    symbol_part = container_name.split('-')[-1]
                    if symbol_part and symbol_part != 'telegram':
                        symbol = symbol_part.upper()
                    elif symbol_part == 'telegram':
                        # This is the telegram bot container, skip it
                        return None
                elif 'telegram' not in container_name.lower():
                    # Try to extract symbol from container name
                    name_parts = container_name.lower().replace('autotrader', '').replace('-', '').replace('_', '')
                    if name_parts:
                        symbol = name_parts.upper()

                # If still unknown and not telegram, it might be a trading bot
                if symbol == "Unknown" and 'telegram' not in container_name.lower():
                    symbol = "TRADING_BOT"

                # Skip telegram container
                if 'telegram' in container_name.lower():
                    return None

                # Parse status
                status = "Unknown"
                start_time = "Unknown"
                if 'Up' in status_info:
                    status = "🟢 Running"
                    # Extract uptime
                    if 'minute' in status_info:
                        import re
                        minutes_match = re.search(r'(\d+)\s+minute', status_info)
                        if minutes_match:
                            start_time = f"{minutes_match.group(1)} phút trước"
                    elif 'hour' in status_info:
                        import re
                        hours_match = re.search(r'(\d+)\s+hour', status_info)
                        if hours_match:
                            start_time = f"{hours_match.group(1)} giờ trước"
                    elif 'day' in status_info:
                        import re
                        days_match = re.search(r'(\d+)\s+day', status_info)
                        if days_match:
                            start_time = f"{days_match.group(1)} ngày trước"
                elif 'Exited' in status_info:
                    status = "🔴 Stopped"
                else:
                    status = "🟡 Unknown"

                # Format the bot info
                bot_info = f"🤖 **{symbol}**\n" \
                          f"💰 Amount: `Đang lấy thông tin...`\n" \
                          f"🔑 Credential: `default`\n" \
                          f"📊 Status: {status}\n" \
                          f"⏰ Runtime: `{start_time}`\n" \
                          f"📦 Container: `{container_name}`"

                self.logger.info(f"Generated bot info: {bot_info}")
                return bot_info

            return None

        except Exception as e:
            self.logger.error(f"Error parsing bot line: {e}")
            return None

    async def _check_prerequisites(self) -> bool:
        """Check if prerequisites for bot creation are met"""
        try:
            # Check if credentials exist
            result = await self._execute_botsh_command([self.bot_script_path, "list-credentials"])

            # Log for debugging
            self.logger.info(f"Credentials check - Return code: {result[0]}")
            self.logger.info(f"Credentials check - Output: {repr(result[1])}")

            # Check if command failed
            if result[0] != 0:
                self.logger.warning("Credentials check failed - command returned non-zero")
                return False

            # Check if output is empty
            if not result[1].strip():
                self.logger.warning("Credentials check failed - empty output")
                return False

            # Check if no credentials found
            if "No credential profiles found" in result[1] or "📭 No credential profiles found" in result[1]:
                self.logger.warning("Credentials check failed - no profiles found")
                return False

            # Check if Docker is running (simple check)
            import subprocess
            import os
            try:
                # Try different docker commands based on environment
                docker_commands = ["docker", "sudo docker"]
                docker_working = False

                for cmd in docker_commands:
                    try:
                        subprocess.run(cmd.split() + ["info"], check=True, capture_output=True)
                        self.logger.info(f"Docker check passed with command: {cmd}")
                        docker_working = True
                        break
                    except (subprocess.CalledProcessError, FileNotFoundError):
                        continue

                if not docker_working:
                    self.logger.warning("Docker check failed - no working docker command found")
                    return False

                return True
            except Exception as e:
                self.logger.warning(f"Docker check failed with exception: {e}")
                return False

        except Exception as e:
            self.logger.error(f"Prerequisites check failed with exception: {e}")
            return False

    async def _execute_botsh_command(self, command: List[str]) -> Tuple[int, str, str]:
        """Execute bot.sh command and return (exit_code, stdout, stderr)"""
        import subprocess
        import asyncio
        
        try:
            # Run command asynchronously
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )
            
            stdout, stderr = await process.communicate()
            
            return (
                process.returncode or 0,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )
            
        except Exception as e:
            self.logger.error(f"Error executing command {' '.join(command)}: {e}")
            return (1, "", str(e))

    # ===============================
    # CreateBot Wizard Methods
    # ===============================

    async def _start_createbot_wizard(self, chat_id: int, context) -> None:
        """Start the createbot wizard with credentials selection"""
        try:
            # Get available credentials
            result = await self._execute_botsh_command([self.bot_script_path, "list-credentials"])

            if result[0] != 0:
                await context.bot.send_message(
                    chat_id,
                    "❌ **Không thể lấy danh sách credentials**\n\n"
                    "Vui lòng thêm credentials trước với `/addcreds`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Parse credentials from output
            credentials = self._parse_credentials_list(result[1])

            if not credentials:
                await context.bot.send_message(
                    chat_id,
                    "❌ **Không có credentials**\n\n"
                    "Vui lòng thêm credentials trước với `/addcreds`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Start wizard session
            user_id = chat_id  # Assuming chat_id is user_id for private chats
            self.session_manager.start_wizard(user_id, 'createbot')
            self.session_manager.update_wizard_data(user_id, 'step', 'select_credentials')
            self.session_manager.update_wizard_data(user_id, 'credentials', credentials)

            # Show credentials selection
            await self._show_credentials_selection(chat_id, context, credentials)

        except Exception as e:
            self.logger.error(f"Error starting createbot wizard: {e}")
            await context.bot.send_message(
                chat_id,
                f"❌ **Lỗi khởi tạo wizard:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    def _parse_credentials_list(self, output: str) -> List[Dict[str, str]]:
        """Parse credentials list from bot.sh output"""
        credentials = []
        lines = output.split('\n')

        for line in lines:
            line = line.strip()
            if line.startswith('• '):
                # Format: • profile_name (Display Name)
                parts = line[2:].strip()
                if '(' in parts and ')' in parts:
                    profile_name = parts.split('(')[0].strip()
                    display_name = parts.split('(')[1].split(')')[0].strip()
                    credentials.append({
                        'profile': profile_name,
                        'display_name': display_name
                    })
                else:
                    # Fallback if no display name
                    credentials.append({
                        'profile': parts,
                        'display_name': parts
                    })

        return credentials

    async def _show_credentials_selection(self, chat_id: int, context, credentials: List[Dict[str, str]]) -> None:
        """Show credentials selection step"""
        try:
            message = "🤖 **Tạo Bot Trading - Bước 1/3**\n\n"
            message += "🔑 **Chọn tài khoản để trade:**\n\n"

            # Create keyboard with credentials
            keyboard = []
            for cred in credentials:
                button_text = f"🔑 {cred['display_name']}"
                callback_data = f"createbot_cred_{cred['profile']}"
                keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

            keyboard.append([InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")])
            reply_markup = InlineKeyboardMarkup(keyboard)

            await context.bot.send_message(
                chat_id,
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            self.logger.error(f"Error showing credentials selection: {e}")
            await context.bot.send_message(
                chat_id,
                f"❌ **Lỗi hiển thị credentials:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_cancel(self, query) -> None:
        """Handle createbot cancellation"""
        user_id = query.from_user.id
        self.session_manager.clear_session(user_id)
        await query.edit_message_text(
            "❌ **Đã hủy tạo bot**\n\n"
            "Sử dụng `/createbot` để thử lại.",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _handle_createbot_credential_selected(self, query, profile: str) -> None:
        """Handle credential selection in createbot wizard"""
        try:
            user_id = query.from_user.id
            session = self.session_manager.get_session(user_id)

            if not session or session.get('wizard_state') != 'createbot':
                await query.edit_message_text(
                    "❌ **Phiên làm việc đã hết hạn**\n\n"
                    "Vui lòng thử lại với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Save selected credential
            self.session_manager.update_wizard_data(user_id, 'selected_profile', profile)
            self.session_manager.update_wizard_data(user_id, 'step', 'select_symbol')

            # Show symbol selection
            await self._show_symbol_selection(query)

        except Exception as e:
            self.logger.error(f"Error handling credential selection: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi xử lý credential:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _show_symbol_selection(self, query) -> None:
        """Show symbol selection step"""
        try:
            message = "🤖 **Tạo Bot Trading - Bước 2/3**\n\n"
            message += "📈 **Chọn symbol để trade:**\n\n"
            message += "**Ví dụ:**\n"
            message += "• `BTCUSDT` (Bitcoin/USDT)\n"
            message += "• `ETHUSDT` (Ethereum/USDT)\n"
            message += "• `BNBUSDT` (BNB/USDT)\n\n"
            message += "💡 Hỗ trợ định dạng đơn giản (btc, eth) hoặc đầy đủ (BTCUSDT)\n\n"
            message += "📝 **Nhập symbol hoặc /cancel để hủy:**"

            keyboard = [
                [InlineKeyboardButton("🔙 Quay lại", callback_data="createbot_back_to_creds"),
                 InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # Update session to expect text input
            user_id = query.from_user.id
            self.session_manager.update_wizard_data(user_id, 'expecting_input', 'symbol')

        except Exception as e:
            self.logger.error(f"Error showing symbol selection: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi hiển thị symbol selection:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_step(self, update: Update, context, session: Dict) -> None:
        """Handle steps in createbot wizard"""
        user_id = update.effective_user.id
        wizard_data = session.get('wizard_data', {})
        expecting_input = wizard_data.get('expecting_input')
        text = update.message.text.strip()

        if expecting_input == 'symbol':
            await self._handle_symbol_input(update, context, text)
        elif expecting_input == 'amount':
            await self._handle_amount_input(update, context, text)

    async def _handle_symbol_input(self, update: Update, context, symbol: str) -> None:
        """Handle symbol input"""
        try:
            user_id = update.effective_user.id

            # Validate symbol format
            if not symbol or len(symbol) < 3:
                await update.message.reply_text(
                    "❌ **Symbol không hợp lệ**\n\n"
                    "Vui lòng nhập symbol hợp lệ (ví dụ: BTCUSDT, ETH, BNB)",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Normalize symbol
            normalized_symbol = symbol.upper()
            if not normalized_symbol.endswith('USDT'):
                normalized_symbol += 'USDT'

            # Save symbol and move to amount step
            self.session_manager.update_wizard_data(user_id, 'symbol', normalized_symbol)
            self.session_manager.update_wizard_data(user_id, 'step', 'enter_amount')
            self.session_manager.update_wizard_data(user_id, 'expecting_input', 'amount')

            # Show amount input
            message = "🤖 **Tạo Bot Trading - Bước 3/3**\n\n"
            message += f"📈 **Symbol:** `{normalized_symbol}`\n\n"
            message += "💰 **Nhập số tiền trade (USD):**\n\n"
            message += "**Ví dụ:**\n"
            message += "• `10` - Trade với $10\n"
            message += "• `50` - Trade với $50\n"
            message += "• `100` - Trade với $100\n\n"
            message += "📝 **Nhập số tiền hoặc /cancel để hủy:**"

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error handling symbol input: {e}")
            await update.message.reply_text(
                f"❌ **Lỗi xử lý symbol:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_amount_input(self, update: Update, context, amount_str: str) -> None:
        """Handle amount input"""
        try:
            user_id = update.effective_user.id

            # Validate amount
            try:
                amount = float(amount_str)
                if amount <= 0:
                    raise ValueError("Amount must be positive")
            except ValueError:
                await update.message.reply_text(
                    "❌ **Số tiền không hợp lệ**\n\n"
                    "Vui lòng nhập số tiền hợp lệ (ví dụ: 10, 50, 100)",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Save amount and show confirmation
            self.session_manager.update_wizard_data(user_id, 'amount', amount)
            self.session_manager.update_wizard_data(user_id, 'expecting_input', None)

            # Show confirmation
            await self._show_createbot_confirmation(update, context)

        except Exception as e:
            self.logger.error(f"Error handling amount input: {e}")
            await update.message.reply_text(
                f"❌ **Lỗi xử lý amount:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _show_createbot_confirmation(self, update: Update, context) -> None:
        """Show createbot confirmation"""
        try:
            user_id = update.effective_user.id
            wizard_data = self.session_manager.get_wizard_data(user_id)

            profile = wizard_data.get('selected_profile', 'unknown')
            symbol = wizard_data.get('symbol', 'unknown')
            amount = wizard_data.get('amount', 0)

            message = "🤖 **Xác nhận tạo Bot Trading**\n\n"
            message += f"🔑 **Tài khoản:** `{profile}`\n"
            message += f"📈 **Symbol:** `{symbol}`\n"
            message += f"💰 **Số tiền:** `${amount}`\n\n"
            message += "✅ **Xác nhận tạo bot?**"

            keyboard = [
                [InlineKeyboardButton("✅ Tạo Bot", callback_data="createbot_final_confirm"),
                 InlineKeyboardButton("📝 Sửa", callback_data="createbot_edit")],
                [InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            self.logger.error(f"Error showing createbot confirmation: {e}")
            await update.message.reply_text(
                f"❌ **Lỗi hiển thị confirmation:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_final_confirm(self, query) -> None:
        """Handle final createbot confirmation"""
        try:
            user_id = query.from_user.id
            session = self.session_manager.get_session(user_id)

            if not session or session.get('wizard_state') != 'createbot':
                await query.edit_message_text(
                    "❌ **Phiên làm việc đã hết hạn**\n\n"
                    "Vui lòng thử lại với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            wizard_data = session.get('wizard_data', {})
            profile = wizard_data.get('selected_profile')
            symbol = wizard_data.get('symbol')
            amount = wizard_data.get('amount')

            if not all([profile, symbol, amount]):
                await query.edit_message_text(
                    "❌ **Thiếu thông tin**\n\n"
                    "Vui lòng thử lại với `/createbot`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Load credentials first
            load_result = await self._execute_botsh_command([
                self.bot_script_path, "load-credentials", profile
            ])

            if load_result[0] != 0:
                await query.edit_message_text(
                    f"❌ **Lỗi load credentials:** ```\n{load_result[2] or load_result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Create bot
            await query.edit_message_text(
                "🔄 **Đang tạo bot...**\n\n"
                "Vui lòng đợi...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Execute bot creation
            result = await self._execute_botsh_command([
                self.bot_script_path, "start", symbol,
                "--amount", str(amount)
            ])

            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ **Bot đã được tạo thành công!**\n\n"
                    f"🔑 **Tài khoản:** `{profile}`\n"
                    f"📈 **Symbol:** `{symbol}`\n"
                    f"💰 **Số tiền:** `${amount}`\n\n"
                    f"🤖 Bot đang khởi động...\n\n"
                    f"💡 Sử dụng `/status {symbol}` để kiểm tra trạng thái",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    f"❌ **Lỗi tạo bot:** ```\n{result[2] or result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )

            # Clear session
            self.session_manager.clear_session(user_id)

        except Exception as e:
            self.logger.error(f"Error in final createbot confirmation: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi tạo bot:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_createbot_edit(self, query) -> None:
        """Handle createbot edit - restart wizard"""
        try:
            user_id = query.from_user.id

            # Clear session and restart
            self.session_manager.clear_session(user_id)

            # Restart wizard
            from types import SimpleNamespace
            fake_update = SimpleNamespace(
                effective_chat=SimpleNamespace(id=query.message.chat.id),
                callback_query=query
            )
            fake_context = SimpleNamespace(bot=query.bot)

            await self.handle_createbot_wizard(fake_update, fake_context)

        except Exception as e:
            self.logger.error(f"Error handling createbot edit: {e}")
            await query.edit_message_text(
                f"❌ **Lỗi restart wizard:** {str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )